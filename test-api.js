// Simple API test script
const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testAPI() {
  console.log('🧪 Testing Event Booking API...\n');

  try {
    // 1. Health Check
    console.log('1. Testing Health Check...');
    const health = await axios.get('http://localhost:5000/health');
    console.log('✅ Health Check:', health.data.message);

    // 2. Register a new user
    console.log('\n2. Registering a new user...');
    const userRegister = await axios.post(`${BASE_URL}/auth/register`, {
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'Test',
      lastName: 'User'
    });
    console.log('✅ User registered:', userRegister.data.data.user.email);
    const userToken = userRegister.data.data.token;

    // 3. Login as admin
    console.log('\n3. Logging in as admin...');
    const adminLogin = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    console.log('✅ Admin logged in:', adminLogin.data.data.user.email);
    const adminToken = adminLogin.data.data.token;

    // 4. Create an event (Admin only)
    console.log('\n4. Creating an event...');
    const eventCreate = await axios.post(`${BASE_URL}/events`, {
      title: 'Tech Conference 2025',
      description: 'A comprehensive technology conference featuring industry leaders',
      date: '2025-09-15',
      time: '09:00',
      location: 'Convention Center, San Francisco',
      totalSeats: 500,
      price: 199.99,
      category: 'Technology'
    }, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    console.log('✅ Event created:', eventCreate.data.data.event.title);
    const eventId = eventCreate.data.data.event.id;

    // 5. Get all events (Public)
    console.log('\n5. Getting all events...');
    const events = await axios.get(`${BASE_URL}/events`);
    console.log('✅ Events retrieved:', events.data.data.events.length, 'events found');

    // 6. Create a booking
    console.log('\n6. Creating a booking...');
    const booking = await axios.post(`${BASE_URL}/bookings`, {
      eventId: eventId,
      numberOfTickets: 2,
      notes: 'Looking forward to the conference!'
    }, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log('✅ Booking created:', booking.data.data.booking.bookingReference);

    // 7. Get user bookings
    console.log('\n7. Getting user bookings...');
    const userBookings = await axios.get(`${BASE_URL}/bookings`, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log('✅ User bookings retrieved:', userBookings.data.data.bookings.length, 'bookings found');

    // 8. Get all bookings (Admin only)
    console.log('\n8. Getting all bookings (Admin)...');
    const allBookings = await axios.get(`${BASE_URL}/bookings/admin/all`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    console.log('✅ All bookings retrieved:', allBookings.data.data.bookings.length, 'total bookings');

    // 9. Update event
    console.log('\n9. Updating event...');
    const eventUpdate = await axios.put(`${BASE_URL}/events/${eventId}`, {
      price: 179.99,
      totalSeats: 600
    }, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    console.log('✅ Event updated, new price:', eventUpdate.data.data.event.price);

    console.log('\n🎉 All tests passed! The Event Booking API is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data?.message || error.message);
    if (error.response?.data?.errors) {
      console.error('Errors:', error.response.data.errors);
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  testAPI();
}

module.exports = testAPI;
