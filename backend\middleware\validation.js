const Joi = require('joi');

/**
 * Middleware to validate request data using Joi schemas
 */
const validate = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    
    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: errorMessage
      });
    }
    
    next();
  };
};

// User validation schemas
const userRegistrationSchema = Joi.object({
  email: Joi.string().email().required().messages({
    'string.email': 'Please provide a valid email address',
    'any.required': 'Email is required'
  }),
  password: Joi.string().min(6).max(100).required().messages({
    'string.min': 'Password must be at least 6 characters long',
    'string.max': 'Password cannot exceed 100 characters',
    'any.required': 'Password is required'
  }),
  firstName: Joi.string().min(2).max(50).required().messages({
    'string.min': 'First name must be at least 2 characters long',
    'string.max': 'First name cannot exceed 50 characters',
    'any.required': 'First name is required'
  }),
  lastName: Joi.string().min(2).max(50).required().messages({
    'string.min': 'Last name must be at least 2 characters long',
    'string.max': 'Last name cannot exceed 50 characters',
    'any.required': 'Last name is required'
  }),
  role: Joi.string().valid('user', 'admin').optional()
});

const userLoginSchema = Joi.object({
  email: Joi.string().email().required().messages({
    'string.email': 'Please provide a valid email address',
    'any.required': 'Email is required'
  }),
  password: Joi.string().required().messages({
    'any.required': 'Password is required'
  })
});

// Event validation schemas
const eventCreationSchema = Joi.object({
  title: Joi.string().min(3).max(200).required().messages({
    'string.min': 'Event title must be at least 3 characters long',
    'string.max': 'Event title cannot exceed 200 characters',
    'any.required': 'Event title is required'
  }),
  description: Joi.string().min(10).max(2000).required().messages({
    'string.min': 'Event description must be at least 10 characters long',
    'string.max': 'Event description cannot exceed 2000 characters',
    'any.required': 'Event description is required'
  }),
  date: Joi.date().greater('now').required().messages({
    'date.greater': 'Event date must be in the future',
    'any.required': 'Event date is required'
  }),
  time: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).required().messages({
    'string.pattern.base': 'Time must be in HH:MM format',
    'any.required': 'Event time is required'
  }),
  location: Joi.string().min(5).max(200).required().messages({
    'string.min': 'Location must be at least 5 characters long',
    'string.max': 'Location cannot exceed 200 characters',
    'any.required': 'Event location is required'
  }),
  totalSeats: Joi.number().integer().min(1).max(10000).required().messages({
    'number.min': 'Total seats must be at least 1',
    'number.max': 'Total seats cannot exceed 10,000',
    'any.required': 'Total seats is required'
  }),
  price: Joi.number().min(0).precision(2).optional().messages({
    'number.min': 'Price cannot be negative'
  }),
  category: Joi.string().min(2).max(50).optional(),
  imageUrl: Joi.string().uri().optional().messages({
    'string.uri': 'Image URL must be a valid URL'
  })
});

const eventUpdateSchema = Joi.object({
  title: Joi.string().min(3).max(200).optional(),
  description: Joi.string().min(10).max(2000).optional(),
  date: Joi.date().greater('now').optional(),
  time: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
  location: Joi.string().min(5).max(200).optional(),
  totalSeats: Joi.number().integer().min(1).max(10000).optional(),
  price: Joi.number().min(0).precision(2).optional(),
  category: Joi.string().min(2).max(50).optional(),
  imageUrl: Joi.string().uri().optional(),
  isActive: Joi.boolean().optional()
});

// Booking validation schemas
const bookingCreationSchema = Joi.object({
  eventId: Joi.number().integer().positive().required().messages({
    'number.positive': 'Event ID must be a positive number',
    'any.required': 'Event ID is required'
  }),
  numberOfTickets: Joi.number().integer().min(1).max(10).required().messages({
    'number.min': 'Number of tickets must be at least 1',
    'number.max': 'Cannot book more than 10 tickets at once',
    'any.required': 'Number of tickets is required'
  }),
  notes: Joi.string().max(500).optional()
});

module.exports = {
  validate,
  userRegistrationSchema,
  userLoginSchema,
  eventCreationSchema,
  eventUpdateSchema,
  bookingCreationSchema
};
