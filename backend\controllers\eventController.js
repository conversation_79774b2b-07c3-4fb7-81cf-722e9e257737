const { Event, User, Booking } = require("../models");
const { asyncHandler } = require("../middleware/errorHandler");
const { Op } = require("sequelize");

/**
 * Get all events with pagination and filtering
 * @route GET /api/events
 * @access Public
 */
const getEvents = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    search,
    category,
    date,
    sortBy = "date",
    sortOrder = "ASC",
  } = req.query;

  const offset = (page - 1) * limit;
  const whereClause = { isActive: true };

  // Add search filter
  if (search) {
    whereClause[Op.or] = [
      { title: { [Op.like]: `%${search}%` } },
      { description: { [Op.like]: `%${search}%` } },
      { location: { [Op.like]: `%${search}%` } },
    ];
  }

  // Add category filter
  if (category) {
    whereClause.category = category;
  }

  // Add date filter
  if (date) {
    const startDate = new Date(date);
    const endDate = new Date(date);
    endDate.setDate(endDate.getDate() + 1);

    whereClause.date = {
      [Op.gte]: startDate,
      [Op.lt]: endDate,
    };
  }

  const { count, rows: events } = await Event.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: User,
        as: "creator",
        attributes: ["id", "firstName", "lastName", "email"],
      },
    ],
    order: [[sortBy, sortOrder.toUpperCase()]],
    limit: parseInt(limit),
    offset: parseInt(offset),
  });

  res.json({
    success: true,
    message: "Events retrieved successfully",
    data: {
      events,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(count / limit),
        totalEvents: count,
        hasNext: page * limit < count,
        hasPrev: page > 1,
      },
    },
  });
});

/**
 * Get single event by ID
 * @route GET /api/events/:id
 * @access Public
 */
const getEventById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const event = await Event.findOne({
    where: { id, isActive: true },
    include: [
      {
        model: User,
        as: "creator",
        attributes: ["id", "firstName", "lastName", "email"],
      },
    ],
  });

  if (!event) {
    return res.status(404).json({
      success: false,
      message: "Event not found",
    });
  }

  res.json({
    success: true,
    message: "Event retrieved successfully",
    data: { event },
  });
});

/**
 * Create new event
 * @route POST /api/events
 * @access Private (Admin only)
 */
const createEvent = asyncHandler(async (req, res) => {
  const eventData = {
    ...req.body,
    createdBy: req.user.id,
    availableSeats: req.body.totalSeats,
  };

  const event = await Event.create(eventData);

  // Get event with creator info
  const eventWithCreator = await Event.findByPk(event.id, {
    include: [
      {
        model: User,
        as: "creator",
        attributes: ["id", "firstName", "lastName", "email"],
      },
    ],
  });

  res.status(201).json({
    success: true,
    message: "Event created successfully",
    data: { event: eventWithCreator },
  });
});

/**
 * Update event
 * @route PUT /api/events/:id
 * @access Private (Admin only or event creator)
 */
const updateEvent = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updateData = req.body;

  const event = await Event.findByPk(id);

  if (!event) {
    return res.status(404).json({
      success: false,
      message: "Event not found",
    });
  }

  // Check if user is admin or event creator
  if (req.user.role !== "admin" && event.createdBy !== req.user.id) {
    return res.status(403).json({
      success: false,
      message: "Access denied. You can only update your own events.",
    });
  }

  // If updating totalSeats, adjust availableSeats accordingly
  if (updateData.totalSeats && updateData.totalSeats !== event.totalSeats) {
    const bookedSeats = event.totalSeats - event.availableSeats;
    updateData.availableSeats = Math.max(
      0,
      updateData.totalSeats - bookedSeats
    );
  }

  await event.update(updateData);

  // Get updated event with creator info
  const updatedEvent = await Event.findByPk(id, {
    include: [
      {
        model: User,
        as: "creator",
        attributes: ["id", "firstName", "lastName", "email"],
      },
    ],
  });

  res.json({
    success: true,
    message: "Event updated successfully",
    data: { event: updatedEvent },
  });
});

/**
 * Delete event
 * @route DELETE /api/events/:id
 * @access Private (Admin only or event creator)
 */
const deleteEvent = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const event = await Event.findByPk(id);

  if (!event) {
    return res.status(404).json({
      success: false,
      message: "Event not found",
    });
  }

  // Check if user is admin or event creator
  if (req.user.role !== "admin" && event.createdBy !== req.user.id) {
    return res.status(403).json({
      success: false,
      message: "Access denied. You can only delete your own events.",
    });
  }

  // Check if event has bookings
  const bookingCount = await Booking.count({
    where: { eventId: id, status: "confirmed" },
  });

  if (bookingCount > 0) {
    return res.status(400).json({
      success: false,
      message:
        "Cannot delete event with existing bookings. Please cancel all bookings first.",
    });
  }

  // Soft delete by setting isActive to false
  await event.update({ isActive: false });

  res.json({
    success: true,
    message: "Event deleted successfully",
  });
});

/**
 * Get event categories
 * @route GET /api/events/categories
 * @access Public
 */
const getEventCategories = asyncHandler(async (req, res) => {
  const categories = await Event.findAll({
    attributes: ["category"],
    where: {
      category: { [Op.not]: null },
      isActive: true,
    },
    group: ["category"],
    raw: true,
  });

  const categoryList = categories.map((cat) => cat.category).filter(Boolean);

  res.json({
    success: true,
    message: "Categories retrieved successfully",
    data: { categories: categoryList },
  });
});

module.exports = {
  getEvents,
  getEventById,
  createEvent,
  updateEvent,
  deleteEvent,
  getEventCategories,
};
