const { Booking, Event, User } = require('../models');
const { asyncHandler } = require('../middleware/errorHandler');
const { sequelize } = require('../config/database');

/**
 * Create a new booking
 * @route POST /api/bookings
 * @access Private
 */
const createBooking = asyncHandler(async (req, res) => {
  const { eventId, numberOfTickets, notes } = req.body;
  const userId = req.user.id;

  // Start transaction
  const transaction = await sequelize.transaction();

  try {
    // Get event with lock to prevent race conditions
    const event = await Event.findByPk(eventId, {
      lock: true,
      transaction
    });

    if (!event) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: 'Event not found'
      });
    }

    if (!event.isActive) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Event is not active'
      });
    }

    // Check if event date is in the future
    if (new Date(event.date) <= new Date()) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Cannot book tickets for past events'
      });
    }

    // Check if enough seats are available
    if (event.availableSeats < numberOfTickets) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: `Only ${event.availableSeats} seats available`
      });
    }

    // Check if user already has a booking for this event
    const existingBooking = await Booking.findOne({
      where: {
        userId,
        eventId,
        status: 'confirmed'
      },
      transaction
    });

    if (existingBooking) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'You already have a booking for this event'
      });
    }

    // Calculate total amount
    const totalAmount = event.price * numberOfTickets;

    // Create booking
    const booking = await Booking.create({
      userId,
      eventId,
      numberOfTickets,
      totalAmount,
      notes,
      status: 'confirmed'
    }, { transaction });

    // Update available seats
    await event.update({
      availableSeats: event.availableSeats - numberOfTickets
    }, { transaction });

    // Commit transaction
    await transaction.commit();

    // Get booking with event and user details
    const bookingWithDetails = await Booking.findByPk(booking.id, {
      include: [
        {
          model: Event,
          as: 'event',
          attributes: ['id', 'title', 'date', 'time', 'location', 'price']
        },
        {
          model: User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName', 'email']
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Booking created successfully',
      data: { booking: bookingWithDetails }
    });

  } catch (error) {
    await transaction.rollback();
    throw error;
  }
});

/**
 * Get user's bookings
 * @route GET /api/bookings
 * @access Private
 */
const getUserBookings = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { page = 1, limit = 10, status } = req.query;

  const offset = (page - 1) * limit;
  const whereClause = { userId };

  if (status) {
    whereClause.status = status;
  }

  const { count, rows: bookings } = await Booking.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: Event,
        as: 'event',
        attributes: ['id', 'title', 'date', 'time', 'location', 'price', 'imageUrl']
      }
    ],
    order: [['createdAt', 'DESC']],
    limit: parseInt(limit),
    offset: parseInt(offset)
  });

  res.json({
    success: true,
    message: 'Bookings retrieved successfully',
    data: {
      bookings,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(count / limit),
        totalBookings: count,
        hasNext: page * limit < count,
        hasPrev: page > 1
      }
    }
  });
});

/**
 * Get booking by ID
 * @route GET /api/bookings/:id
 * @access Private
 */
const getBookingById = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user.id;

  const booking = await Booking.findOne({
    where: { id },
    include: [
      {
        model: Event,
        as: 'event',
        attributes: ['id', 'title', 'description', 'date', 'time', 'location', 'price', 'imageUrl']
      },
      {
        model: User,
        as: 'user',
        attributes: ['id', 'firstName', 'lastName', 'email']
      }
    ]
  });

  if (!booking) {
    return res.status(404).json({
      success: false,
      message: 'Booking not found'
    });
  }

  // Check if user owns the booking or is admin
  if (booking.userId !== userId && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: 'Access denied'
    });
  }

  res.json({
    success: true,
    message: 'Booking retrieved successfully',
    data: { booking }
  });
});

/**
 * Cancel booking
 * @route PUT /api/bookings/:id/cancel
 * @access Private
 */
const cancelBooking = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user.id;

  // Start transaction
  const transaction = await sequelize.transaction();

  try {
    const booking = await Booking.findOne({
      where: { id },
      include: [{ model: Event, as: 'event' }],
      lock: true,
      transaction
    });

    if (!booking) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    // Check if user owns the booking or is admin
    if (booking.userId !== userId && req.user.role !== 'admin') {
      await transaction.rollback();
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    if (booking.status === 'cancelled') {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Booking is already cancelled'
      });
    }

    // Check if event date is in the future (allow cancellation up to event time)
    if (new Date(booking.event.date) <= new Date()) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Cannot cancel booking for past events'
      });
    }

    // Update booking status
    await booking.update({ status: 'cancelled' }, { transaction });

    // Return seats to available pool
    await booking.event.update({
      availableSeats: booking.event.availableSeats + booking.numberOfTickets
    }, { transaction });

    await transaction.commit();

    res.json({
      success: true,
      message: 'Booking cancelled successfully',
      data: { booking }
    });

  } catch (error) {
    await transaction.rollback();
    throw error;
  }
});

/**
 * Get all bookings (Admin only)
 * @route GET /api/bookings/admin/all
 * @access Private (Admin only)
 */
const getAllBookings = asyncHandler(async (req, res) => {
  const { page = 1, limit = 10, status, eventId } = req.query;

  const offset = (page - 1) * limit;
  const whereClause = {};

  if (status) {
    whereClause.status = status;
  }

  if (eventId) {
    whereClause.eventId = eventId;
  }

  const { count, rows: bookings } = await Booking.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: Event,
        as: 'event',
        attributes: ['id', 'title', 'date', 'time', 'location', 'price']
      },
      {
        model: User,
        as: 'user',
        attributes: ['id', 'firstName', 'lastName', 'email']
      }
    ],
    order: [['createdAt', 'DESC']],
    limit: parseInt(limit),
    offset: parseInt(offset)
  });

  res.json({
    success: true,
    message: 'All bookings retrieved successfully',
    data: {
      bookings,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(count / limit),
        totalBookings: count,
        hasNext: page * limit < count,
        hasPrev: page > 1
      }
    }
  });
});

module.exports = {
  createBooking,
  getUserBookings,
  getBookingById,
  cancelBooking,
  getAllBookings
};
