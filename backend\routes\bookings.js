const express = require('express');
const router = express.Router();

const {
  createBooking,
  getUserBookings,
  getBookingById,
  cancelBooking,
  getAllBookings
} = require('../controllers/bookingController');

const { authenticate, requireAdmin } = require('../middleware/auth');
const { validate, bookingCreationSchema } = require('../middleware/validation');

// Protected routes (Authenticated users)
router.post('/', authenticate, validate(bookingCreationSchema), createBooking);
router.get('/', authenticate, getUserBookings);
router.get('/:id', authenticate, getBookingById);
router.put('/:id/cancel', authenticate, cancelBooking);

// Admin only routes
router.get('/admin/all', authenticate, requireAdmin, getAllBookings);

module.exports = router;
