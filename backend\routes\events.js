const express = require('express');
const router = express.Router();

const {
  getEvents,
  getEventById,
  createEvent,
  updateEvent,
  deleteEvent,
  getEventCategories
} = require('../controllers/eventController');

const { authenticate, requireAdmin, optionalAuth } = require('../middleware/auth');
const { validate, eventCreationSchema, eventUpdateSchema } = require('../middleware/validation');

// Public routes
router.get('/', optionalAuth, getEvents);
router.get('/categories', getEventCategories);
router.get('/:id', getEventById);

// Protected routes (Admin only)
router.post('/', authenticate, requireAdmin, validate(eventCreationSchema), createEvent);
router.put('/:id', authenticate, requireAdmin, validate(eventUpdateSchema), updateEvent);
router.delete('/:id', authenticate, requireAdmin, deleteEvent);

module.exports = router;
