const { sequelize } = require("../config/database");
const User = require("./User");
const Event = require("./Event");
const Booking = require("./Booking");

// Define associations
User.hasMany(Event, {
  foreignKey: "createdBy",
  as: "createdEvents",
});

Event.belongsTo(User, {
  foreignKey: "createdBy",
  as: "creator",
});

User.hasMany(Booking, {
  foreignKey: "userId",
  as: "bookings",
});

Booking.belongsTo(User, {
  foreignKey: "userId",
  as: "user",
});

Event.hasMany(Booking, {
  foreignKey: "eventId",
  as: "bookings",
});

Booking.belongsTo(Event, {
  foreignKey: "eventId",
  as: "event",
});

// Sync database
const syncDatabase = async (force = false) => {
  try {
    await sequelize.sync({ force });
    console.log("✅ Database synchronized successfully.");

    // Create default admin user if not exists
    await createDefaultAdmin();
  } catch (error) {
    console.error("❌ Error synchronizing database:", error);
    throw error;
  }
};

// Create default admin user
const createDefaultAdmin = async () => {
  try {
    const adminExists = await User.findOne({
      where: { email: "<EMAIL>" },
    });

    if (!adminExists) {
      await User.create({
        email: "<EMAIL>",
        password: "admin123",
        firstName: "Admin",
        lastName: "User",
        role: "admin",
      });
      console.log(
        "✅ Default admin user created: <EMAIL> / admin123"
      );
    }
  } catch (error) {
    console.error("❌ Error creating default admin:", error);
  }
};

module.exports = {
  sequelize,
  User,
  Event,
  Booking,
  syncDatabase,
};
