# Event Booking System API

A fully functional RESTful API for an Event Booking System built with Node.js, Express, and Sequelize ORM. Users can browse events, book tickets, and manage their bookings with role-based access control.

## 🚀 Features

### User Management

- ✅ User registration and authentication
- ✅ JWT-based secure login
- ✅ Role-based access (User, Admin)
- ✅ Password hashing with bcrypt
- ✅ Profile management

### Event Management

- ✅ Create, read, update, delete events (Admin only)
- ✅ Public event browsing (no authentication required)
- ✅ Event filtering and search
- ✅ Category-based organization
- ✅ Seat availability tracking

### Booking System

- ✅ Authenticated users can book tickets
- ✅ View personal bookings
- ✅ Cancel bookings
- ✅ Prevent overbooking
- ✅ Automatic seat management
- ✅ Booking reference generation

### Security & Performance

- ✅ JWT authentication
- ✅ Input validation with Joi
- ✅ Rate limiting
- ✅ CORS protection
- ✅ Helmet security headers
- ✅ SQL injection prevention
- ✅ Transaction support for data consistency

## 🛠️ Tech Stack

- **Backend**: Node.js, Express.js
- **Database**: SQLite (configurable to PostgreSQL/MySQL)
- **ORM**: Sequelize
- **Authentication**: JWT (JSON Web Tokens)
- **Validation**: Joi
- **Security**: Helmet, CORS, bcryptjs
- **Environment**: dotenv

## 📁 Project Structure

```
Event-Booking-App/
├── backend/
│   ├── config/
│   │   ├── database.js          # Database configuration
│   │   └── jwt.js               # JWT utilities
│   ├── controllers/
│   │   ├── authController.js    # Authentication logic
│   │   ├── eventController.js   # Event management
│   │   └── bookingController.js # Booking operations
│   ├── middleware/
│   │   ├── auth.js              # Authentication middleware
│   │   ├── validation.js        # Input validation
│   │   └── errorHandler.js      # Error handling
│   ├── models/
│   │   ├── User.js              # User model
│   │   ├── Event.js             # Event model
│   │   ├── Booking.js           # Booking model
│   │   └── index.js             # Model associations
│   ├── routes/
│   │   ├── auth.js              # Auth routes
│   │   ├── events.js            # Event routes
│   │   └── bookings.js          # Booking routes
│   ├── utils/
│   │   └── helpers.js           # Utility functions
│   ├── .env                     # Environment variables
│   ├── app.js                   # Express app setup
│   └── server.js                # Server startup
├── frontend/                    # React frontend (optional)
└── package.json                 # Main package.json
```

## 🚀 Quick Start

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd Event-Booking-App
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Environment Setup**

   ```bash
   # Copy environment file
   cp backend/.env.example backend/.env

   # Edit the .env file with your configuration
   ```

4. **Start the server**

   ```bash
   # Development mode with auto-reload
   npm run dev

   # Production mode
   npm start
   ```

5. **Access the API**
   - API Base URL: `http://localhost:5000`
   - API Documentation: `http://localhost:5000/api`
   - Health Check: `http://localhost:5000/health`

### Default Admin Account

- **Email**: <EMAIL>
- **Password**: admin123

## 📚 API Documentation

### Authentication Endpoints

#### Register User

```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "John",
  "lastName": "Doe",
  "role": "user" // optional, defaults to "user"
}
```

#### Login User

```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Get Profile (Protected)

```http
GET /api/auth/profile
Authorization: Bearer <jwt_token>
```

#### Update Profile (Protected)

```http
PUT /api/auth/profile
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Smith",
  "email": "<EMAIL>"
}
```

### Event Endpoints

#### Get All Events (Public)

```http
GET /api/events?page=1&limit=10&search=concert&category=music&sortBy=date&sortOrder=ASC
```

#### Get Event by ID (Public)

```http
GET /api/events/:id
```

#### Create Event (Admin Only)

```http
POST /api/events
Authorization: Bearer <admin_jwt_token>
Content-Type: application/json

{
  "title": "Summer Music Festival",
  "description": "A fantastic music festival featuring top artists",
  "date": "2024-07-15",
  "time": "18:00",
  "location": "Central Park, New York",
  "totalSeats": 5000,
  "price": 75.00,
  "category": "Music",
  "imageUrl": "https://example.com/image.jpg"
}
```

#### Update Event (Admin Only)

```http
PUT /api/events/:id
Authorization: Bearer <admin_jwt_token>
Content-Type: application/json

{
  "title": "Updated Event Title",
  "price": 80.00
}
```

#### Delete Event (Admin Only)

```http
DELETE /api/events/:id
Authorization: Bearer <admin_jwt_token>
```

### Booking Endpoints

#### Create Booking (Protected)

```http
POST /api/bookings
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "eventId": 1,
  "numberOfTickets": 2,
  "notes": "Special seating request"
}
```

#### Get User Bookings (Protected)

```http
GET /api/bookings?page=1&limit=10&status=confirmed
Authorization: Bearer <jwt_token>
```

#### Get Booking by ID (Protected)

```http
GET /api/bookings/:id
Authorization: Bearer <jwt_token>
```

#### Cancel Booking (Protected)

```http
PUT /api/bookings/:id/cancel
Authorization: Bearer <jwt_token>
```

#### Get All Bookings (Admin Only)

```http
GET /api/bookings/admin/all?page=1&limit=10&status=confirmed&eventId=1
Authorization: Bearer <admin_jwt_token>
```

## 🧪 Testing the API

### Using cURL

1. **Register a new user:**

```bash
curl -X POST http://localhost:5000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "Test",
    "lastName": "User"
  }'
```

2. **Login and get token:**

```bash
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123"
  }'
```

3. **Create an event (Admin):**

```bash
curl -X POST http://localhost:5000/api/events \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{
    "title": "Test Event",
    "description": "A test event for demonstration",
    "date": "2024-12-31",
    "time": "20:00",
    "location": "Test Venue",
    "totalSeats": 100,
    "price": 25.00,
    "category": "Test"
  }'
```

4. **Book a ticket:**

```bash
curl -X POST http://localhost:5000/api/bookings \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_USER_TOKEN" \
  -d '{
    "eventId": 1,
    "numberOfTickets": 2
  }'
```

### Using Postman

1. Import the API endpoints into Postman
2. Set up environment variables for base URL and tokens
3. Test each endpoint with different scenarios

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the `backend` directory:

```env
# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration (SQLite by default)
DB_DIALECT=sqlite
# For PostgreSQL/MySQL, uncomment and configure:
# DB_NAME=event_booking_db
# DB_USER=your_username
# DB_PASSWORD=your_password
# DB_HOST=localhost
# DB_PORT=5432

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production
JWT_EXPIRES_IN=7d

# CORS Configuration
FRONTEND_URL=http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### Database Configuration

The API uses SQLite by default for easy setup. To use PostgreSQL or MySQL:

1. Install the appropriate database driver:

```bash
# For PostgreSQL
npm install pg pg-hstore

# For MySQL
npm install mysql2
```

2. Update your `.env` file with database credentials
3. Change `DB_DIALECT` to `postgres` or `mysql`

## 🚀 Deployment

### Production Setup

1. **Set environment to production:**

```env
NODE_ENV=production
```

2. **Use a production database:**

```env
DB_DIALECT=postgres
DB_NAME=your_production_db
DB_USER=your_db_user
DB_PASSWORD=your_secure_password
DB_HOST=your_db_host
```

3. **Secure your JWT secret:**

```env
JWT_SECRET=your_very_secure_random_secret_key
```

4. **Configure CORS for your frontend:**

```env
FRONTEND_URL=https://your-frontend-domain.com
```

### Docker Deployment (Optional)

Create a `Dockerfile` in the backend directory:

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 5000
CMD ["npm", "start"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

## 🆘 Support

If you encounter any issues or have questions:

1. Check the API documentation at `http://localhost:5000/api`
2. Review the error messages in the API responses
3. Check the server logs for detailed error information
4. Ensure all required fields are provided in requests
5. Verify JWT tokens are valid and not expired
