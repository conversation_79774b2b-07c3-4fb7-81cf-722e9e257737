{"name": "event-booking-app", "private": true, "version": "1.0.0", "type": "commonjs", "scripts": {"start": "node backend/server.js", "dev": "nodemon backend/server.js", "frontend:dev": "cd frontend && npm run dev", "frontend:build": "cd frontend && npm run build", "test": "jest"}, "dependencies": {"express": "^4.18.2", "sequelize": "^6.35.2", "sqlite3": "^5.1.6", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "joi": "^17.11.0", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}}